import 'package:flutter/material.dart';
import '../models/character_class.dart';

/// 职业选择组件
/// 显示所有可选职业，支持选择和查看详细信息
class ClassSelector extends StatelessWidget {
  final CharacterClass? selectedClass;
  final Function(CharacterClass) onClassSelected;

  const ClassSelector({
    super.key,
    this.selectedClass,
    required this.onClassSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 职业选择网格
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            childAspectRatio: 2.5,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
          ),
          itemCount: CharacterClass.allClasses.length,
          itemBuilder: (context, index) {
            final characterClass = CharacterClass.allClasses[index];
            final isSelected = selectedClass?.name == characterClass.name;

            return _buildClassCard(context, characterClass, isSelected);
          },
        ),
        
        // 选中职业的详细信息
        if (selectedClass != null) ...[
          const SizedBox(height: 16),
          _buildClassDetails(context, selectedClass!),
        ],
      ],
    );
  }

  /// 构建职业卡片
  Widget _buildClassCard(BuildContext context, CharacterClass characterClass, bool isSelected) {
    return Card(
      elevation: isSelected ? 8 : 2,
      color: isSelected 
          ? Theme.of(context).colorScheme.primaryContainer
          : null,
      child: InkWell(
        onTap: () => onClassSelected(characterClass),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // 职业图标
              Text(
                characterClass.icon,
                style: const TextStyle(fontSize: 24),
              ),
              const SizedBox(height: 4),
              // 职业名称
              Text(
                characterClass.displayName,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: isSelected 
                      ? Theme.of(context).colorScheme.onPrimaryContainer
                      : null,
                ),
                textAlign: TextAlign.center,
              ),
              // 选中指示器
              if (isSelected)
                Icon(
                  Icons.check_circle,
                  color: Theme.of(context).colorScheme.primary,
                  size: 16,
                ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建职业详细信息
  Widget _buildClassDetails(BuildContext context, CharacterClass characterClass) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primaryContainer.withOpacity(0.3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题
          Row(
            children: [
              Text(
                characterClass.icon,
                style: const TextStyle(fontSize: 24),
              ),
              const SizedBox(width: 8),
              Text(
                '选中职业：${characterClass.displayName}',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          
          // 职业描述
          Text(
            characterClass.description,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          const SizedBox(height: 8),
          
          // 护甲限制
          _buildDetailRow(
            context,
            '护甲限制',
            characterClass.armorRestriction,
            Icons.shield,
            Colors.blue,
          ),
          const SizedBox(height: 4),
          
          // 专精技能
          _buildDetailRow(
            context,
            '专精技能',
            '${characterClass.specialSkill} +3',
            Icons.star,
            Colors.amber,
          ),
          const SizedBox(height: 4),
          
          // 起始金币
          _buildDetailRow(
            context,
            '起始金币',
            '${characterClass.startingGold}gp',
            Icons.monetization_on,
            Colors.green,
          ),
          const SizedBox(height: 4),
          
          // 职业特点
          _buildDetailRow(
            context,
            '职业特点',
            characterClass.characteristics,
            Icons.info,
            Colors.purple,
          ),
          const SizedBox(height: 8),
          
          // 专精技能说明
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.amber.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: Colors.amber.withOpacity(0.3),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Icon(
                      Icons.help_outline,
                      size: 16,
                      color: Colors.amber,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '技能说明：',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Colors.amber,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  characterClass.specialSkillDescription,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.8),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建详细信息行
  Widget _buildDetailRow(
    BuildContext context,
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          icon,
          size: 16,
          color: color,
        ),
        const SizedBox(width: 8),
        Expanded(
          child: RichText(
            text: TextSpan(
              style: Theme.of(context).textTheme.bodySmall,
              children: [
                TextSpan(
                  text: '$label：',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
                TextSpan(
                  text: value,
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
