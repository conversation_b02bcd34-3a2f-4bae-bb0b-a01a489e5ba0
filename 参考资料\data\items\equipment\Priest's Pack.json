{"desc": [], "special": [], "index": "priests-pack", "name": "Priest's Pack", "equipment_category": {"index": "adventuring-gear", "name": "Adventuring Gear", "url": "/api/2014/equipment-categories/adventuring-gear"}, "gear_category": {"index": "equipment-packs", "name": "Equipment Packs", "url": "/api/2014/equipment-categories/equipment-packs"}, "cost": {"quantity": 19, "unit": "gp"}, "contents": [{"item": {"index": "backpack", "name": "Backpack", "url": "/api/2014/equipment/backpack"}, "quantity": 1}, {"item": {"index": "blanket", "name": "Blanket", "url": "/api/2014/equipment/blanket"}, "quantity": 1}, {"item": {"index": "candle", "name": "Candle", "url": "/api/2014/equipment/candle"}, "quantity": 10}, {"item": {"index": "tinderbox", "name": "Tinderbox", "url": "/api/2014/equipment/tinderbox"}, "quantity": 1}, {"item": {"index": "rations-1-day", "name": "Rations (1 day)", "url": "/api/2014/equipment/rations-1-day"}, "quantity": 2}, {"item": {"index": "waterskin", "name": "<PERSON><PERSON>", "url": "/api/2014/equipment/waterskin"}, "quantity": 1}, {"item": {"index": "alms-box", "name": "Alms box", "url": "/api/2014/equipment/alms-box"}, "quantity": 1}, {"item": {"index": "block-of-incense", "name": "Block of incense", "url": "/api/2014/equipment/block-of-incense"}, "quantity": 2}, {"item": {"index": "censer", "name": "<PERSON><PERSON><PERSON>", "url": "/api/2014/equipment/censer"}, "quantity": 1}, {"item": {"index": "vestments", "name": "Vestments", "url": "/api/2014/equipment/vestments"}, "quantity": 1}], "url": "/api/2014/equipment/priests-pack", "updated_at": "2025-04-08T21:13:58.828Z", "properties": []}