import 'package:flutter/material.dart';
import '../models/character.dart';
import '../services/character_service.dart';
import '../services/logger_service.dart';
import '../widgets/character_card.dart';
import 'character_creation_screen.dart';

/// 角色选择界面
/// 显示所有保存的角色，支持创建、选择和删除角色
class CharacterSelectionScreen extends StatefulWidget {
  const CharacterSelectionScreen({super.key});

  @override
  State<CharacterSelectionScreen> createState() => _CharacterSelectionScreenState();
}

class _CharacterSelectionScreenState extends State<CharacterSelectionScreen> {
  List<Character> _characters = [];
  Character? _selectedCharacter;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    LoggerService.enterScreen('CharacterSelectionScreen');
    _loadCharacters();
  }

  @override
  void dispose() {
    LoggerService.exitScreen('CharacterSelectionScreen');
    super.dispose();
  }

  /// 加载所有角色
  Future<void> _loadCharacters() async {
    try {
      setState(() {
        _isLoading = true;
      });

      final characters = await CharacterService.getAllCharacters();
      final selectedCharacter = await CharacterService.getSelectedCharacter();

      setState(() {
        _characters = characters;
        _selectedCharacter = selectedCharacter;
        _isLoading = false;
      });

      LoggerService.info(LogTags.ui, 'Loaded ${characters.length} characters');
    } catch (e, stackTrace) {
      LoggerService.error(LogTags.ui, 'Failed to load characters', e, stackTrace);
      setState(() {
        _isLoading = false;
      });
      
      if (mounted) {
        _showErrorSnackBar('加载角色失败，请重试');
      }
    }
  }

  /// 选择角色
  Future<void> _selectCharacter(Character character) async {
    LoggerService.userAction('CharacterSelectionScreen', 'select_character', {
      'characterId': character.id,
      'characterName': character.name,
    });

    try {
      await CharacterService.setSelectedCharacter(character.id);
      setState(() {
        _selectedCharacter = character;
      });

      if (mounted) {
        _showSuccessSnackBar('已选择角色：${character.name}');
      }
    } catch (e, stackTrace) {
      LoggerService.error(LogTags.ui, 'Failed to select character', e, stackTrace);
      if (mounted) {
        _showErrorSnackBar('选择角色失败，请重试');
      }
    }
  }

  /// 删除角色
  Future<void> _deleteCharacter(Character character) async {
    LoggerService.userAction('CharacterSelectionScreen', 'delete_character', {
      'characterId': character.id,
      'characterName': character.name,
    });

    // 显示确认对话框
    final confirmed = await _showDeleteConfirmDialog(character);
    if (!confirmed) return;

    try {
      final success = await CharacterService.deleteCharacter(character.id);
      if (success) {
        await _loadCharacters(); // 重新加载角色列表
        if (mounted) {
          _showSuccessSnackBar('角色已删除：${character.name}');
        }
      } else {
        if (mounted) {
          _showErrorSnackBar('删除角色失败，请重试');
        }
      }
    } catch (e, stackTrace) {
      LoggerService.error(LogTags.ui, 'Failed to delete character', e, stackTrace);
      if (mounted) {
        _showErrorSnackBar('删除角色失败，请重试');
      }
    }
  }

  /// 创建新角色
  Future<void> _createCharacter() async {
    LoggerService.userAction('CharacterSelectionScreen', 'create_character');

    final result = await Navigator.push<Character>(
      context,
      MaterialPageRoute(
        builder: (context) => const CharacterCreationScreen(),
      ),
    );

    if (result != null) {
      await _loadCharacters(); // 重新加载角色列表
      if (mounted) {
        _showSuccessSnackBar('角色创建成功：${result.name}');
      }
    }
  }

  /// 进入游戏
  Future<void> _enterGame() async {
    if (_selectedCharacter == null) {
      _showErrorSnackBar('请先选择一个角色');
      return;
    }

    LoggerService.userAction('CharacterSelectionScreen', 'enter_game', {
      'characterId': _selectedCharacter!.id,
      'characterName': _selectedCharacter!.name,
    });

    // TODO: 导航到游戏主界面
    _showInfoSnackBar('进入游戏功能尚未实现');
  }

  /// 显示删除确认对话框
  Future<bool> _showDeleteConfirmDialog(Character character) async {
    return await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认删除'),
        content: Text('确定要删除角色"${character.name}"吗？此操作无法撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('删除'),
          ),
        ],
      ),
    ) ?? false;
  }

  /// 显示成功消息
  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  /// 显示错误消息
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  /// 显示信息消息
  void _showInfoSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.blue,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('角色选择'),
        centerTitle: true,
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // 内容区域
                Expanded(
                  child: _characters.isEmpty
                      ? _buildEmptyState()
                      : _buildCharacterList(),
                ),
                // 底部操作栏
                _buildActionBar(),
              ],
            ),
    );
  }

  /// 构建空状态界面
  Widget _buildEmptyState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.person_add,
            size: 64,
            color: Colors.grey,
          ),
          SizedBox(height: 16),
          Text(
            '还没有角色',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey,
            ),
          ),
          SizedBox(height: 8),
          Text(
            '点击下方"创建角色"按钮开始创建你的第一个角色',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// 构建角色列表
  Widget _buildCharacterList() {
    return RefreshIndicator(
      onRefresh: _loadCharacters,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _characters.length,
        itemBuilder: (context, index) {
          final character = _characters[index];
          final isSelected = _selectedCharacter?.id == character.id;

          return Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: CharacterCard(
              character: character,
              isSelected: isSelected,
              onTap: () => _selectCharacter(character),
              onDelete: () => _deleteCharacter(character),
            ),
          );
        },
      ),
    );
  }

  /// 构建底部操作栏
  Widget _buildActionBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          // 创建角色按钮
          Expanded(
            child: ElevatedButton.icon(
              onPressed: _createCharacter,
              icon: const Icon(Icons.add),
              label: const Text('创建角色'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
          const SizedBox(width: 12),
          // 进入游戏按钮
          Expanded(
            child: ElevatedButton.icon(
              onPressed: _selectedCharacter != null ? _enterGame : null,
              icon: const Icon(Icons.play_arrow),
              label: const Text('进入游戏'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 12),
                backgroundColor: _selectedCharacter != null 
                    ? Theme.of(context).colorScheme.primary
                    : null,
              ),
            ),
          ),
          const SizedBox(width: 12),
          // 删除角色按钮
          ElevatedButton.icon(
            onPressed: _selectedCharacter != null 
                ? () => _deleteCharacter(_selectedCharacter!)
                : null,
            icon: const Icon(Icons.delete),
            label: const Text('删除'),
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
              foregroundColor: Colors.red,
            ),
          ),
        ],
      ),
    );
  }
}
