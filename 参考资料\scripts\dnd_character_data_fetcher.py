#!/usr/bin/env python3
"""
D&D 5e 角色数据获取脚本
专门获取角色创建相关的重要数据：种族、职业、背景等
从DnD5eAPI获取完整的角色创建数据
"""

import requests
import json
import time
from pathlib import Path
import re

class DnDCharacterDataFetcher:
    def __init__(self):
        self.dnd5e_base = "https://www.dnd5eapi.co/api/2014"
    
    def sanitize_filename(self, name):
        """清理文件名，移除非法字符"""
        name = re.sub(r'[<>:"/\\|?*]', '_', name)
        name = name.strip()
        return name
    
    def fetch_races_from_dnd5e_api(self, output_dir="data/races"):
        """从DnD5eAPI获取所有种族数据"""
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        print("🧝 正在从DnD5eAPI获取种族数据...")
        print("📚 包含所有可用种族：人类、精灵、矮人、龙裔、提夫林等...")
        
        try:
            # 首先获取种族列表
            response = requests.get(f"{self.dnd5e_base}/races")
            response.raise_for_status()
            races_list = response.json()
            
            print(f"🎯 找到 {races_list['count']} 个种族")
            
            saved_count = 0
            skipped_count = 0
            
            # 遍历每个种族，获取详细信息
            for i, race_ref in enumerate(races_list['results'], 1):
                race_index = race_ref['index']
                race_name = race_ref['name']
                
                print(f"  [{i:2d}/{races_list['count']}] 正在获取种族: {race_name}")
                
                # 获取种族详细信息
                detail_response = requests.get(f"{self.dnd5e_base}/races/{race_index}")
                detail_response.raise_for_status()
                race_detail = detail_response.json()
                
                # 保存种族数据
                safe_name = self.sanitize_filename(race_name)
                filename = f"{safe_name}.json"
                filepath = output_path / filename
                
                if filepath.exists():
                    print(f"    ⏭️  文件已存在，跳过")
                    skipped_count += 1
                    continue
                
                try:
                    with open(filepath, 'w', encoding='utf-8') as f:
                        json.dump(race_detail, f, ensure_ascii=False, indent=2)
                    print(f"    ✅ 保存成功: {filename}")
                    saved_count += 1
                except Exception as e:
                    print(f"    ❌ 保存失败 {race_name}：{e}")
                
                time.sleep(0.3)  # 避免请求过快
            
            print(f"\n🎉 种族数据获取完成！")
            print(f"✨ 新保存文件：{saved_count} 个种族")
            print(f"⏭️  跳过已存在：{skipped_count} 个种族")
            print(f"📂 保存位置：{output_path}")
            
            return saved_count, skipped_count
            
        except requests.RequestException as e:
            print(f"❌ 获取种族数据失败：{e}")
            return 0, 0
    
    def fetch_classes_from_dnd5e_api(self, output_dir="data/classes"):
        """从DnD5eAPI获取所有职业数据"""
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        print("⚔️ 正在从DnD5eAPI获取职业数据...")
        print("📚 包含所有可用职业：战士、法师、牧师、盗贼、野蛮人等...")
        
        try:
            # 首先获取职业列表
            response = requests.get(f"{self.dnd5e_base}/classes")
            response.raise_for_status()
            classes_list = response.json()
            
            print(f"🎯 找到 {classes_list['count']} 个职业")
            
            saved_count = 0
            skipped_count = 0
            
            # 遍历每个职业，获取详细信息
            for i, class_ref in enumerate(classes_list['results'], 1):
                class_index = class_ref['index']
                class_name = class_ref['name']
                
                print(f"  [{i:2d}/{classes_list['count']}] 正在获取职业: {class_name}")
                
                # 获取职业详细信息
                detail_response = requests.get(f"{self.dnd5e_base}/classes/{class_index}")
                detail_response.raise_for_status()
                class_detail = detail_response.json()
                
                # 保存职业数据
                safe_name = self.sanitize_filename(class_name)
                filename = f"{safe_name}.json"
                filepath = output_path / filename
                
                if filepath.exists():
                    print(f"    ⏭️  文件已存在，跳过")
                    skipped_count += 1
                    continue
                
                try:
                    with open(filepath, 'w', encoding='utf-8') as f:
                        json.dump(class_detail, f, ensure_ascii=False, indent=2)
                    print(f"    ✅ 保存成功: {filename}")
                    saved_count += 1
                except Exception as e:
                    print(f"    ❌ 保存失败 {class_name}：{e}")
                
                time.sleep(0.3)  # 避免请求过快
            
            print(f"\n🎉 职业数据获取完成！")
            print(f"✨ 新保存文件：{saved_count} 个职业")
            print(f"⏭️  跳过已存在：{skipped_count} 个职业")
            print(f"📂 保存位置：{output_path}")
            
            return saved_count, skipped_count
            
        except requests.RequestException as e:
            print(f"❌ 获取职业数据失败：{e}")
            return 0, 0
    
    def fetch_backgrounds_from_dnd5e_api(self, output_dir="data/backgrounds"):
        """从DnD5eAPI获取所有背景数据"""
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        print("📜 正在从DnD5eAPI获取背景数据...")
        print("📚 包含角色背景：学者、士兵、罪犯、贵族等...")
        
        try:
            # 首先获取背景列表
            response = requests.get(f"{self.dnd5e_base}/backgrounds")
            response.raise_for_status()
            backgrounds_list = response.json()
            
            print(f"🎯 找到 {backgrounds_list['count']} 个背景")
            
            saved_count = 0
            skipped_count = 0
            
            # 遍历每个背景，获取详细信息
            for i, bg_ref in enumerate(backgrounds_list['results'], 1):
                bg_index = bg_ref['index']
                bg_name = bg_ref['name']
                
                print(f"  [{i:2d}/{backgrounds_list['count']}] 正在获取背景: {bg_name}")
                
                # 获取背景详细信息
                detail_response = requests.get(f"{self.dnd5e_base}/backgrounds/{bg_index}")
                detail_response.raise_for_status()
                bg_detail = detail_response.json()
                
                # 保存背景数据
                safe_name = self.sanitize_filename(bg_name)
                filename = f"{safe_name}.json"
                filepath = output_path / filename
                
                if filepath.exists():
                    print(f"    ⏭️  文件已存在，跳过")
                    skipped_count += 1
                    continue
                
                try:
                    with open(filepath, 'w', encoding='utf-8') as f:
                        json.dump(bg_detail, f, ensure_ascii=False, indent=2)
                    print(f"    ✅ 保存成功: {filename}")
                    saved_count += 1
                except Exception as e:
                    print(f"    ❌ 保存失败 {bg_name}：{e}")
                
                time.sleep(0.3)  # 避免请求过快
            
            print(f"\n🎉 背景数据获取完成！")
            print(f"✨ 新保存文件：{saved_count} 个背景")
            print(f"⏭️  跳过已存在：{skipped_count} 个背景")
            print(f"📂 保存位置：{output_path}")
            
            return saved_count, skipped_count
            
        except requests.RequestException as e:
            print(f"❌ 获取背景数据失败：{e}")
            return 0, 0
    
    def fetch_subclasses_from_dnd5e_api(self, output_dir="data/subclasses"):
        """从DnD5eAPI获取所有子职业数据"""
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        print("🎭 正在从DnD5eAPI获取子职业数据...")
        print("📚 包含职业专精：战斗大师、塑能师、生命牧师等...")
        
        try:
            # 首先获取子职业列表
            response = requests.get(f"{self.dnd5e_base}/subclasses")
            response.raise_for_status()
            subclasses_list = response.json()
            
            print(f"🎯 找到 {subclasses_list['count']} 个子职业")
            
            saved_count = 0
            skipped_count = 0
            
            # 遍历每个子职业，获取详细信息
            for i, sub_ref in enumerate(subclasses_list['results'], 1):
                sub_index = sub_ref['index']
                sub_name = sub_ref['name']
                
                print(f"  [{i:3d}/{subclasses_list['count']}] 正在获取子职业: {sub_name}")
                
                # 获取子职业详细信息
                detail_response = requests.get(f"{self.dnd5e_base}/subclasses/{sub_index}")
                detail_response.raise_for_status()
                sub_detail = detail_response.json()
                
                # 保存子职业数据
                safe_name = self.sanitize_filename(sub_name)
                filename = f"{safe_name}.json"
                filepath = output_path / filename
                
                if filepath.exists():
                    print(f"    ⏭️  文件已存在，跳过")
                    skipped_count += 1
                    continue
                
                try:
                    with open(filepath, 'w', encoding='utf-8') as f:
                        json.dump(sub_detail, f, ensure_ascii=False, indent=2)
                    print(f"    ✅ 保存成功: {filename}")
                    saved_count += 1
                except Exception as e:
                    print(f"    ❌ 保存失败 {sub_name}：{e}")
                
                # 显示进度
                if i % 20 == 0:
                    print(f"    📊 进度: {i}/{subclasses_list['count']} ({i/subclasses_list['count']*100:.1f}%)")
                
                time.sleep(0.3)  # 避免请求过快
            
            print(f"\n🎉 子职业数据获取完成！")
            print(f"✨ 新保存文件：{saved_count} 个子职业")
            print(f"⏭️  跳过已存在：{skipped_count} 个子职业")
            print(f"📂 保存位置：{output_path}")
            
            return saved_count, skipped_count
            
        except requests.RequestException as e:
            print(f"❌ 获取子职业数据失败：{e}")
            return 0, 0
    
    def fetch_all_character_data(self):
        """获取所有角色创建相关数据"""
        print("🎲 开始获取D&D 5e角色创建数据...")
        print("=" * 60)
        
        # 1. 获取种族数据
        print("\n🧝 第一步：获取种族数据")
        race_saved, race_skipped = self.fetch_races_from_dnd5e_api()
        
        # 2. 获取职业数据
        print("\n⚔️ 第二步：获取职业数据")
        class_saved, class_skipped = self.fetch_classes_from_dnd5e_api()
        
        # 3. 获取背景数据
        print("\n📜 第三步：获取背景数据")
        bg_saved, bg_skipped = self.fetch_backgrounds_from_dnd5e_api()
        
        # 4. 获取子职业数据
        print("\n🎭 第四步：获取子职业数据")
        sub_saved, sub_skipped = self.fetch_subclasses_from_dnd5e_api()
        
        # 总结
        print("\n" + "=" * 60)
        print("🎉 所有角色创建数据获取完成！")
        print(f"🧝 种族数据：保存 {race_saved} 个，跳过 {race_skipped} 个")
        print(f"⚔️ 职业数据：保存 {class_saved} 个，跳过 {class_skipped} 个")
        print(f"📜 背景数据：保存 {bg_saved} 个，跳过 {bg_skipped} 个")
        print(f"🎭 子职业数据：保存 {sub_saved} 个，跳过 {sub_skipped} 个")
        print(f"📁 数据保存在：./data/ 目录下")
        print("\n🌟 现在您可以创建完整的D&D角色了！")

def main():
    fetcher = DnDCharacterDataFetcher()
    
    print("🎲 D&D 5e角色数据获取工具")
    print("=" * 50)
    print("专门获取角色创建相关数据：")
    print("  🧝 种族数据 (Races) - 人类、精灵、矮人等")
    print("  ⚔️ 职业数据 (Classes) - 战士、法师、牧师等")
    print("  📜 背景数据 (Backgrounds) - 学者、士兵、贵族等")
    print("  🎭 子职业数据 (Subclasses) - 各种专精方向")
    print("\n各数据分类保存为单独的JSON文件")
    print("已存在的文件将被跳过")
    
    print("\n选择操作：")
    print("1 - 获取所有角色数据（推荐）")
    print("2 - 仅获取种族数据 🧝")
    print("3 - 仅获取职业数据 ⚔️")
    print("4 - 仅获取背景数据 📜")
    print("5 - 仅获取子职业数据 🎭")
    
    choice = input("\n请选择 (1-5): ").strip()
    
    if choice == '1':
        fetcher.fetch_all_character_data()
    elif choice == '2':
        print("🧝 开始获取种族数据...")
        fetcher.fetch_races_from_dnd5e_api()
    elif choice == '3':
        print("⚔️ 开始获取职业数据...")
        fetcher.fetch_classes_from_dnd5e_api()
    elif choice == '4':
        print("📜 开始获取背景数据...")
        fetcher.fetch_backgrounds_from_dnd5e_api()
    elif choice == '5':
        print("🎭 开始获取子职业数据...")
        fetcher.fetch_subclasses_from_dnd5e_api()
    else:
        print("无效选择，已取消")

if __name__ == "__main__":
    main()