# 功能-广场

## 概述
广场是玩家进入游戏世界后的第一个场景，也是连接各个功能区域的中心枢纽。

## 入口
玩家选择角色，再点击"进入世界"后进入广场。（程序上通过token，知道是哪一个玩家的哪一个角色）

## 界面结构
广场界面遵循通用界面模板的设计，包含以下四个主要区域：

### 1. 地点条（顶部固定）
- 左侧显示广场图标 🏙️
- 中间显示当前地点名称："广场"

### 2. 内容区（中间区域）
中间是可以进入的设施区：
玩家点击设施后选中设施，在下方操作区出现进入按钮，玩家点击进入可以进入对应的设施
玩家点击选中后的设施，则取消选中。
- **商店**：玩家买卖东西的地方
  - 图标：🏪
  - 描述：买卖装备和物品
- **神殿**：玩家治疗和升级的地方
  - 图标：🏛️
  - 描述：治疗和升级
- **传送点**：玩家通过传送点，传送到副本进行冒险
  - 图标：🌀
  - 描述：传送到副本进行冒险

### 3. 队伍条（底部固定）
显示当前玩家和队友信息（最多显示4人）：
- **玩家信息卡**：
  - 头像区：头像图标，性别（固定在头像的左下方），姓名（固定在头像上方，底板半透明）
  - 状态区：血条（显示当前血量/最大血量），魔法条（显示当前魔法/最大魔法）
- **队友信息**（如有）：
  - 简化显示：头像、名称、生命值

### 4. 操作区（最底部固定）
广场场景特有的操作按钮：
- **法术**：显示可用法术列表，点击后可以使用
  - 图标：✨
  - 备注：根据职业判断是否可用，不可用时显示为灰色
- **道具**：显示背包中的道具，点击后可以使用
  - 图标：🎒
- **装备**：查看和更换角色装备
  - 图标：🛡️

## 交互逻辑

### 设施点击
玩家点击任意设施图标后：
1. 表示选中设施，在下方操作区最左边出现"进入"按钮
2. 玩家点进"进入"按钮，进入游戏。

### 操作区交互
- **法术按钮**：点击后显示法术列表模态框，可选择和使用法术
- **道具按钮**：点击后显示道具列表模态框，可使用道具
- **装备按钮**：点击后显示装备界面模态框，可查看和更换装备

## 多人在线支持
广场中可能需要显示其他在线玩家，以增强社交体验：
- 其他玩家以简化形式显示在内容区的适当位置
- 可考虑添加社交操作（如打招呼、组队邀请等）

## 响应式设计
广场UI需要适配不同设备：
1. **PC网页版本**：充分利用宽屏空间，展示更多信息
2. **平板设备**：调整元素大小和间距，确保合理布局
3. **移动设备**：优化触控体验，确保所有元素足够大且易于点击

## 视觉反馈
所有UI元素需要有明显的视觉反馈：
1. 设施图标悬停时有放大或上浮效果
2. 按钮点击时有明显的按下状态
3. 不可用的操作显示为灰色

## 注意事项
1. 广场UI要适配移动设备和PC网页版本
2. 所有UI元素需要有明显的视觉反馈
3. 为支持多人在线，广场可能需要显示其他在线玩家


