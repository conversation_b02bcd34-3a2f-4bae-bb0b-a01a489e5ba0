#!/usr/bin/env python3
"""
增强版D&D 5e 数据获取脚本
从Open5e API和DnD5eAPI获取完整的D&D数据，包括法术、物品、技能等
重点：法术数据(300+个)是最重要的数据
"""

import requests
import json
import time
from pathlib import Path
import re

class EnhancedDnDDataFetcher:
    def __init__(self):
        self.open5e_base = "https://api.open5e.com"
        self.dnd5e_base = "https://www.dnd5eapi.co/api/2014"
    
    def sanitize_filename(self, name):
        """清理文件名，移除非法字符"""
        name = re.sub(r'[<>:"/\\|?*]', '_', name)
        name = name.strip()
        return name
    
    def fetch_spells_from_dnd5e_api(self, output_dir="data/spells"):
        """从DnD5eAPI获取所有法术数据（重点功能）"""
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        print("🔮 正在从DnD5eAPI获取法术数据...")
        print("📚 预计有300+个法术，这是最重要的数据，请耐心等待...")
        
        try:
            # 首先获取法术列表
            response = requests.get(f"{self.dnd5e_base}/spells")
            response.raise_for_status()
            spells_list = response.json()
            
            print(f"🎯 找到 {spells_list['count']} 个法术")
            
            saved_count = 0
            skipped_count = 0
            
            # 遍历每个法术，获取详细信息
            for i, spell_ref in enumerate(spells_list['results'], 1):
                spell_index = spell_ref['index']
                spell_name = spell_ref['name']
                
                print(f"  [{i:3d}/{spells_list['count']}] 正在获取法术: {spell_name}")
                
                # 获取法术详细信息
                detail_response = requests.get(f"{self.dnd5e_base}/spells/{spell_index}")
                detail_response.raise_for_status()
                spell_detail = detail_response.json()
                
                # 保存法术数据
                safe_name = self.sanitize_filename(spell_name)
                filename = f"{safe_name}.json"
                filepath = output_path / filename
                
                if filepath.exists():
                    print(f"    ⏭️  文件已存在，跳过")
                    skipped_count += 1
                    continue
                
                try:
                    with open(filepath, 'w', encoding='utf-8') as f:
                        json.dump(spell_detail, f, ensure_ascii=False, indent=2)
                    print(f"    ✅ 保存成功: {filename}")
                    saved_count += 1
                except Exception as e:
                    print(f"    ❌ 保存失败 {spell_name}：{e}")
                
                # 显示进度
                if i % 50 == 0:
                    print(f"    📊 进度: {i}/{spells_list['count']} ({i/spells_list['count']*100:.1f}%)")
                
                time.sleep(0.2)  # 避免请求过快，法术很多需要控制频率
            
            print(f"\n🎉 法术数据获取完成！")
            print(f"✨ 新保存文件：{saved_count} 个法术")
            print(f"⏭️  跳过已存在：{skipped_count} 个法术")
            print(f"📂 保存位置：{output_path}")
            
            return saved_count, skipped_count
            
        except requests.RequestException as e:
            print(f"❌ 获取法术数据失败：{e}")
            return 0, 0
    
    def fetch_skills_from_dnd5e_api(self, output_dir="data/skills"):
        """从DnD5eAPI获取所有技能数据"""
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        print("🎯 正在从DnD5eAPI获取技能数据...")
        
        try:
            # 首先获取技能列表
            response = requests.get(f"{self.dnd5e_base}/skills")
            response.raise_for_status()
            skills_list = response.json()
            
            print(f"找到 {skills_list['count']} 个技能 (应该是18个)")
            
            saved_count = 0
            skipped_count = 0
            
            # 遍历每个技能，获取详细信息
            for skill_ref in skills_list['results']:
                skill_index = skill_ref['index']
                skill_name = skill_ref['name']
                
                print(f"  正在获取技能: {skill_name}")
                
                # 获取技能详细信息
                detail_response = requests.get(f"{self.dnd5e_base}/skills/{skill_index}")
                detail_response.raise_for_status()
                skill_detail = detail_response.json()
                
                # 保存技能数据
                safe_name = self.sanitize_filename(skill_name)
                filename = f"{safe_name}.json"
                filepath = output_path / filename
                
                if filepath.exists():
                    print(f"    文件已存在，跳过")
                    skipped_count += 1
                    continue
                
                try:
                    with open(filepath, 'w', encoding='utf-8') as f:
                        json.dump(skill_detail, f, ensure_ascii=False, indent=2)
                    print(f"    保存成功: {filename}")
                    saved_count += 1
                except Exception as e:
                    print(f"    保存失败 {skill_name}：{e}")
                
                time.sleep(0.3)  # 避免请求过快
            
            print(f"\n✅ 技能数据获取完成！")
            print(f"新保存文件：{saved_count} 个技能")
            print(f"跳过已存在：{skipped_count} 个技能")
            
            return saved_count, skipped_count
            
        except requests.RequestException as e:
            print(f"获取技能数据失败：{e}")
            return 0, 0
    
    def fetch_additional_dnd5e_data(self, output_dir="data"):
        """从DnD5eAPI获取其他有用的数据"""
        base_output_path = Path(output_dir)
        
        # 定义要获取的其他数据类型（更完整的列表）
        additional_endpoints = [
            # 核心角色数据
            ('races', '种族'),
            ('classes', '职业'), 
            ('backgrounds', '背景'),
            ('monsters', '怪物'),
            ('equipment', '装备'),
            ('magic-items', '魔法物品'),
            ('feats', '专长'),
            ('features', '特性'),
            ('subclasses', '子职业'),
            ('subraces', '子种族'),
            ('traits', '种族特质'),
            
            # 游戏机制
            ('ability-scores', '属性值'),
            ('conditions', '状态'),
            ('damage-types', '伤害类型'),
            ('languages', '语言'),
            ('proficiencies', '熟练项'),
            ('weapon-properties', '武器属性'),
            ('alignments', '阵营'),
            ('magic-schools', '法术学派'),
            ('equipment-categories', '装备类别'),
            ('rule-sections', '规则章节'),
        ]
        
        total_saved = 0
        total_skipped = 0
        
        for endpoint, data_type in additional_endpoints:
            print(f"\n正在获取{data_type}数据...")
            
            type_output_path = base_output_path / endpoint.replace('-', '_')
            type_output_path.mkdir(parents=True, exist_ok=True)
            
            try:
                # 获取数据列表
                response = requests.get(f"{self.dnd5e_base}/{endpoint}")
                response.raise_for_status()
                data_list = response.json()
                
                print(f"找到 {data_list['count']} 个{data_type}")
                
                saved_count = 0
                skipped_count = 0
                
                # 遍历每个项目，获取详细信息
                for item_ref in data_list['results']:
                    item_index = item_ref['index']
                    item_name = item_ref['name']
                    
                    print(f"  正在获取{data_type}: {item_name}")
                    
                    # 获取详细信息
                    detail_response = requests.get(f"{self.dnd5e_base}/{endpoint}/{item_index}")
                    detail_response.raise_for_status()
                    item_detail = detail_response.json()
                    
                    # 保存数据
                    safe_name = self.sanitize_filename(item_name)
                    filename = f"{safe_name}.json"
                    filepath = type_output_path / filename
                    
                    if filepath.exists():
                        print(f"    文件已存在，跳过")
                        skipped_count += 1
                        continue
                    
                    try:
                        with open(filepath, 'w', encoding='utf-8') as f:
                            json.dump(item_detail, f, ensure_ascii=False, indent=2)
                        print(f"    保存成功: {filename}")
                        saved_count += 1
                    except Exception as e:
                        print(f"    保存失败 {item_name}：{e}")
                    
                    time.sleep(0.3)  # 避免请求过快
                
                total_saved += saved_count
                total_skipped += skipped_count
                
                print(f"  {data_type} - 新保存：{saved_count}，跳过：{skipped_count}")
                
            except requests.RequestException as e:
                print(f"  获取{data_type}数据失败：{e}")
        
        return total_saved, total_skipped
    
    def fetch_and_save_items(self, output_dir="data/items", page_size=100):
        """从Open5e API获取所有类型的物品数据并批量保存"""
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 定义要获取的物品类型端点
        item_endpoints = [
            ('magicitems', '魔法物品'),
            ('weapons', '武器'),
            ('armor', '护甲')
        ]
        
        total_fetched = 0
        total_saved = 0
        total_skipped = 0
        
        print("📦 正在从Open5e API获取物品数据...")
        
        for endpoint, item_type in item_endpoints:
            print(f"\n正在获取{item_type}数据...")
            
            # 为每种物品类型创建子目录
            type_output_path = output_path / endpoint
            type_output_path.mkdir(parents=True, exist_ok=True)
            
            page = 1
            
            while True:
                # 根据端点版本选择合适的URL
                if endpoint == 'magicitems':
                    url = f"{self.open5e_base}/v1/{endpoint}/"
                else:
                    url = f"{self.open5e_base}/v2/{endpoint}/"
                    
                params = {
                    'page_size': page_size,
                    'page': page
                }
                
                try:
                    response = requests.get(url, params=params)
                    response.raise_for_status()
                    data = response.json()
                    
                    items = data['results']
                    total_fetched += len(items)
                    
                    print(f"  第 {page} 页：获取到 {len(items)} 个{item_type}")
                    
                    # 保存这一批物品
                    batch_saved, batch_skipped = self.save_items_batch(items, type_output_path, item_type)
                    total_saved += batch_saved
                    total_skipped += batch_skipped
                    
                    print(f"    - 已保存：{batch_saved} 个")
                    print(f"    - 已跳过：{batch_skipped} 个")
                    
                    if not data.get('next'):
                        break
                    
                    page += 1
                    time.sleep(0.5)  # 避免请求过快
                    
                except requests.RequestException as e:
                    print(f"  请求失败：{e}")
                    break
        
        print(f"\n✅ Open5e物品数据获取完成！")
        print(f"总共获取：{total_fetched} 个物品")
        print(f"新保存文件：{total_saved} 个物品")
        print(f"跳过已存在：{total_skipped} 个物品")
        
        return total_fetched, total_saved, total_skipped
    
    def save_items_batch(self, items, output_path, item_type="物品"):
        """保存一批物品数据"""
        saved_count = 0
        skipped_count = 0
        
        for item in items:
            name = item.get('name', 'Unknown')
            safe_name = self.sanitize_filename(name)
            filename = f"{safe_name}.json"
            filepath = output_path / filename
            
            # 检查文件是否已存在
            if filepath.exists():
                skipped_count += 1
                continue
            
            try:
                with open(filepath, 'w', encoding='utf-8') as f:
                    json.dump(item, f, ensure_ascii=False, indent=2)
                saved_count += 1
            except Exception as e:
                print(f"    保存失败 {name}：{e}")
        
        return saved_count, skipped_count
    
    def fetch_all_data(self):
        """获取所有数据：法术、物品、技能和其他游戏数据"""
        print("🎲 开始获取完整的D&D 5e数据...")
        print("=" * 60)
        
        # 1. 获取法术数据（最重要）
        print("\n🔮 第一步：获取法术数据（DnD5eAPI）- 最重要的数据！")
        spell_saved, spell_skipped = self.fetch_spells_from_dnd5e_api()
        
        # 2. 获取物品数据
        print("\n📦 第二步：获取物品数据（Open5e API）")
        item_fetched, item_saved, item_skipped = self.fetch_and_save_items()
        
        # 3. 获取技能数据
        print("\n🎯 第三步：获取技能数据（DnD5eAPI）")
        skill_saved, skill_skipped = self.fetch_skills_from_dnd5e_api()
        
        # 4. 获取其他游戏数据
        print("\n📋 第四步：获取其他游戏数据（DnD5eAPI）")
        other_saved, other_skipped = self.fetch_additional_dnd5e_data()
        
        # 总结
        print("\n" + "=" * 60)
        print("🎉 所有D&D 5e数据获取完成！")
        print(f"🔮 法术数据：保存 {spell_saved} 个，跳过 {spell_skipped} 个")
        print(f"📦 物品数据：获取 {item_fetched} 个，保存 {item_saved} 个，跳过 {item_skipped} 个")
        print(f"🎯 技能数据：保存 {skill_saved} 个，跳过 {skill_skipped} 个")
        print(f"📋 其他数据：保存 {other_saved} 个，跳过 {other_skipped} 个")
        print(f"📁 数据保存在：./data/ 目录下")
        print("\n🌟 火球术、治疗轻伤等经典法术都在其中！")

def main():
    fetcher = EnhancedDnDDataFetcher()
    
    print("🎲 增强版D&D 5e数据获取工具")
    print("=" * 50)
    print("包括：")
    print("  🔮 法术数据 (300+个) - DnD5eAPI ⭐ 重点推荐！")
    print("  📦 物品数据（魔法物品、武器、护甲）- Open5e API")
    print("  🎯 技能数据 (18个) - DnD5eAPI")
    print("  📋 其他游戏数据（属性、状态、语言等）- DnD5eAPI")
    print("\n各数据分类保存为单独的JSON文件")
    print("已存在的文件将被跳过")
    
    print("\n选择操作：")
    print("1 - 获取所有数据（推荐）")
    print("2 - 仅获取法术数据 ⭐ (火球术、治疗轻伤等)")
    print("3 - 仅获取物品数据")
    print("4 - 仅获取技能数据")
    print("5 - 仅获取其他游戏数据")
    
    choice = input("\n请选择 (1-5): ").strip()
    
    if choice == '1':
        fetcher.fetch_all_data()
    elif choice == '2':
        print("🔮 开始获取法术数据...")
        fetcher.fetch_spells_from_dnd5e_api()
    elif choice == '3':
        fetcher.fetch_and_save_items()
    elif choice == '4':
        fetcher.fetch_skills_from_dnd5e_api()
    elif choice == '5':
        fetcher.fetch_additional_dnd5e_data()
    else:
        print("无效选择，已取消")

if __name__ == "__main__":
    main()