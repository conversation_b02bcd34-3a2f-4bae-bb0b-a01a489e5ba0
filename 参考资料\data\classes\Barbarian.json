{"index": "barbarian", "name": "Barbarian", "hit_die": 12, "proficiency_choices": [{"desc": "Choose two from Animal Handling, Athletics, Intimidation, Nature, Perception, and Survival", "choose": 2, "type": "proficiencies", "from": {"option_set_type": "options_array", "options": [{"option_type": "reference", "item": {"index": "skill-animal-handling", "name": "Skill: Animal Handling", "url": "/api/2014/proficiencies/skill-animal-handling"}}, {"option_type": "reference", "item": {"index": "skill-athletics", "name": "Skill: Athletics", "url": "/api/2014/proficiencies/skill-athletics"}}, {"option_type": "reference", "item": {"index": "skill-intimidation", "name": "Skill: Intimidation", "url": "/api/2014/proficiencies/skill-intimidation"}}, {"option_type": "reference", "item": {"index": "skill-nature", "name": "Skill: Nature", "url": "/api/2014/proficiencies/skill-nature"}}, {"option_type": "reference", "item": {"index": "skill-perception", "name": "Skill: Perception", "url": "/api/2014/proficiencies/skill-perception"}}, {"option_type": "reference", "item": {"index": "skill-survival", "name": "Skill: Survival", "url": "/api/2014/proficiencies/skill-survival"}}]}}], "proficiencies": [{"index": "light-armor", "name": "Light Armor", "url": "/api/2014/proficiencies/light-armor"}, {"index": "medium-armor", "name": "Medium Armor", "url": "/api/2014/proficiencies/medium-armor"}, {"index": "shields", "name": "Shields", "url": "/api/2014/proficiencies/shields"}, {"index": "simple-weapons", "name": "Simple Weapons", "url": "/api/2014/proficiencies/simple-weapons"}, {"index": "martial-weapons", "name": "Martial Weapons", "url": "/api/2014/proficiencies/martial-weapons"}, {"index": "saving-throw-str", "name": "Saving Throw: STR", "url": "/api/2014/proficiencies/saving-throw-str"}, {"index": "saving-throw-con", "name": "Saving Throw: CON", "url": "/api/2014/proficiencies/saving-throw-con"}], "saving_throws": [{"index": "str", "name": "STR", "url": "/api/2014/ability-scores/str"}, {"index": "con", "name": "CON", "url": "/api/2014/ability-scores/con"}], "starting_equipment": [{"equipment": {"index": "explorers-pack", "name": "Explorer's Pack", "url": "/api/2014/equipment/explorers-pack"}, "quantity": 1}, {"equipment": {"index": "javelin", "name": "Javelin", "url": "/api/2014/equipment/javelin"}, "quantity": 4}], "starting_equipment_options": [{"desc": "(a) a greataxe or (b) any martial melee weapon", "choose": 1, "type": "equipment", "from": {"option_set_type": "options_array", "options": [{"option_type": "counted_reference", "count": 1, "of": {"index": "greataxe", "name": "Greataxe", "url": "/api/2014/equipment/greataxe"}}, {"option_type": "choice", "choice": {"desc": "any martial melee weapon", "choose": 1, "type": "equipment", "from": {"option_set_type": "equipment_category", "equipment_category": {"index": "martial-melee-weapons", "name": "<PERSON>", "url": "/api/2014/equipment-categories/martial-melee-weapons"}}}}]}}, {"desc": "(a) two handaxes or (b) any simple weapon", "choose": 1, "type": "equipment", "from": {"option_set_type": "options_array", "options": [{"option_type": "counted_reference", "count": 2, "of": {"index": "handaxe", "name": "Handaxe", "url": "/api/2014/equipment/handaxe"}}, {"option_type": "choice", "choice": {"desc": "any simple weapon", "choose": 1, "type": "equipment", "from": {"option_set_type": "equipment_category", "equipment_category": {"index": "simple-weapons", "name": "Simple Weapons", "url": "/api/2014/equipment-categories/simple-weapons"}}}}]}}], "class_levels": "/api/2014/classes/barbarian/levels", "multi_classing": {"prerequisites": [{"ability_score": {"index": "str", "name": "STR", "url": "/api/2014/ability-scores/str"}, "minimum_score": 13}], "proficiencies": [{"index": "shields", "name": "Shields", "url": "/api/2014/proficiencies/shields"}, {"index": "simple-weapons", "name": "Simple Weapons", "url": "/api/2014/proficiencies/simple-weapons"}, {"index": "martial-weapons", "name": "Martial Weapons", "url": "/api/2014/proficiencies/martial-weapons"}]}, "subclasses": [{"index": "berserker", "name": "<PERSON><PERSON><PERSON><PERSON>", "url": "/api/2014/subclasses/berserker"}], "url": "/api/2014/classes/barbarian", "updated_at": "2025-04-08T21:13:56.155Z"}