{"index": "cleric", "name": "Cleric", "hit_die": 8, "proficiency_choices": [{"desc": "Choose two from History, Insight, Medicine, Persuasion, and Religion", "choose": 2, "type": "proficiencies", "from": {"option_set_type": "options_array", "options": [{"option_type": "reference", "item": {"index": "skill-history", "name": "Skill: History", "url": "/api/2014/proficiencies/skill-history"}}, {"option_type": "reference", "item": {"index": "skill-insight", "name": "Skill: Insight", "url": "/api/2014/proficiencies/skill-insight"}}, {"option_type": "reference", "item": {"index": "skill-medicine", "name": "Skill: Medicine", "url": "/api/2014/proficiencies/skill-medicine"}}, {"option_type": "reference", "item": {"index": "skill-persuasion", "name": "Skill: Per<PERSON><PERSON><PERSON>", "url": "/api/2014/proficiencies/skill-persuasion"}}, {"option_type": "reference", "item": {"index": "skill-religion", "name": "Skill: Religion", "url": "/api/2014/proficiencies/skill-religion"}}]}}], "proficiencies": [{"index": "light-armor", "name": "Light Armor", "url": "/api/2014/proficiencies/light-armor"}, {"index": "medium-armor", "name": "Medium Armor", "url": "/api/2014/proficiencies/medium-armor"}, {"index": "shields", "name": "Shields", "url": "/api/2014/proficiencies/shields"}, {"index": "simple-weapons", "name": "Simple Weapons", "url": "/api/2014/proficiencies/simple-weapons"}, {"index": "saving-throw-wis", "name": "Saving Throw: WIS", "url": "/api/2014/proficiencies/saving-throw-wis"}, {"index": "saving-throw-cha", "name": "Saving Throw: CHA", "url": "/api/2014/proficiencies/saving-throw-cha"}], "saving_throws": [{"index": "wis", "name": "WIS", "url": "/api/2014/ability-scores/wis"}, {"index": "cha", "name": "CHA", "url": "/api/2014/ability-scores/cha"}], "starting_equipment": [{"equipment": {"index": "shield", "name": "Shield", "url": "/api/2014/equipment/shield"}, "quantity": 1}], "starting_equipment_options": [{"desc": "(a) a mace or (b) a warhammer (if proficient)", "choose": 1, "type": "equipment", "from": {"option_set_type": "options_array", "options": [{"option_type": "counted_reference", "count": 1, "of": {"index": "mace", "name": "Mace", "url": "/api/2014/equipment/mace"}}, {"option_type": "counted_reference", "count": 1, "of": {"index": "warhammer", "name": "Warhammer", "url": "/api/2014/equipment/warhammer"}, "prerequisites": [{"type": "proficiency", "proficiency": {"index": "warhammers", "name": "Warhammers", "url": "/api/2014/proficiencies/warhammers"}}]}]}}, {"desc": "(a) scale mail, (b) leather armor, or (c) chain mail (if proficient)", "choose": 1, "type": "equipment", "from": {"option_set_type": "options_array", "options": [{"option_type": "counted_reference", "count": 1, "of": {"index": "scale-mail", "name": "Scale Mail", "url": "/api/2014/equipment/scale-mail"}}, {"option_type": "counted_reference", "count": 1, "of": {"index": "leather-armor", "name": "<PERSON><PERSON> Armor", "url": "/api/2014/equipment/leather-armor"}}, {"option_type": "counted_reference", "count": 1, "of": {"index": "chain-mail", "name": "Chain Mail", "url": "/api/2014/equipment/chain-mail"}, "prerequisites": [{"type": "proficiency", "proficiency": {"index": "chain-mail", "name": "Chain Mail", "url": "/api/2014/proficiencies/chain-mail"}}]}]}}, {"desc": "(a) a light crossbow and 20 bolts or (b) any simple weapon", "choose": 1, "type": "equipment", "from": {"option_set_type": "options_array", "options": [{"option_type": "multiple", "items": [{"option_type": "counted_reference", "count": 1, "of": {"index": "crossbow-light", "name": "Crossbow, light", "url": "/api/2014/equipment/crossbow-light"}}, {"option_type": "counted_reference", "count": 20, "of": {"index": "crossbow-bolt", "name": "Crossbow bolt", "url": "/api/2014/equipment/crossbow-bolt"}}]}, {"option_type": "choice", "choice": {"desc": "any simple weapon", "choose": 1, "type": "equipment", "from": {"option_set_type": "equipment_category", "equipment_category": {"index": "simple-weapons", "name": "Simple Weapons", "url": "/api/2014/equipment-categories/simple-weapons"}}}}]}}, {"desc": "(a) a priest’s pack or (b) an explorer’s pack", "choose": 1, "type": "equipment", "from": {"option_set_type": "options_array", "options": [{"option_type": "counted_reference", "count": 1, "of": {"index": "priests-pack", "name": "Priest's Pack", "url": "/api/2014/equipment/priests-pack"}}, {"option_type": "counted_reference", "count": 1, "of": {"index": "explorers-pack", "name": "Explorer's Pack", "url": "/api/2014/equipment/explorers-pack"}}]}}, {"desc": "holy symbol", "choose": 1, "type": "equipment", "from": {"option_set_type": "equipment_category", "equipment_category": {"index": "holy-symbols", "name": "Holy Symbols", "url": "/api/2014/equipment-categories/holy-symbols"}}}], "class_levels": "/api/2014/classes/cleric/levels", "multi_classing": {"prerequisites": [{"ability_score": {"index": "wis", "name": "WIS", "url": "/api/2014/ability-scores/wis"}, "minimum_score": 13}], "proficiencies": [{"index": "light-armor", "name": "Light Armor", "url": "/api/2014/proficiencies/light-armor"}, {"index": "medium-armor", "name": "Medium Armor", "url": "/api/2014/proficiencies/medium-armor"}, {"index": "shields", "name": "Shields", "url": "/api/2014/proficiencies/shields"}]}, "subclasses": [{"index": "life", "name": "Life", "url": "/api/2014/subclasses/life"}], "spellcasting": {"level": 1, "spellcasting_ability": {"index": "wis", "name": "WIS", "url": "/api/2014/ability-scores/wis"}, "info": [{"name": "Cantrips", "desc": ["At 1st level, you know three cantrips of your choice from the cleric spell list. You learn additional cleric cantrips of your choice at higher levels, as shown in the Cantrips Known column of the Cleric table."]}, {"name": "Preparing and Casting Spells", "desc": ["The Cleric table shows how many spell slots you have to cast your spells of 1st level and higher. To cast one of these spells, you must expend a slot of the spell's level or higher. You regain all expended spell slots when you finish a long rest.", "You prepare the list of cleric spells that are available for you to cast, choosing from the cleric spell list. When you do so, choose a number of cleric spells equal to your Wisdom modifier + your cleric level (minimum of one spell). The spells must be of a level for which you have spell slots.", "For example, if you are a 3rd-level cleric, you have four 1st-level and two 2nd-level spell slots. With a Wisdom of 16, your list of prepared spells can include six spells of 1st or 2nd level, in any combination. If you prepare the 1st-level spell cure wounds, you can cast it using a 1st-level or 2nd-level slot. Casting the spell doesn't remove it from your list of prepared spells.", "You can change your list of prepared spells when you finish a long rest. Preparing a new list of cleric spells requires time spent in prayer and meditation: at least 1 minute per spell level for each spell on your list."]}, {"name": "Spellcasting Ability", "desc": ["Wisdom is your spellcasting ability for your cleric spells. The power of your spells comes from your devotion to your deity. You use your Wisdom whenever a cleric spell refers to your spellcasting ability. In addition, you use your Wisdom modifier when setting the saving throw DC for a cleric spell you cast and when making an attack roll with one.", "Spell save DC = 8 + your proficiency bonus + your Wisdom modifier", "Spell attack modifier = your proficiency bonus + your Wisdom modifier"]}, {"name": "Ritual Casting", "desc": ["You can cast a cleric spell as a ritual if that spell has the ritual tag and you have the spell prepared."]}, {"name": "Spellcasting Focus", "desc": ["You can use a holy symbol (see Equipment) as a spellcasting focus for your cleric spells."]}]}, "spells": "/api/2014/classes/cleric/spells", "url": "/api/2014/classes/cleric", "updated_at": "2025-04-08T21:13:56.155Z"}