import 'package:flutter/material.dart';
import '../models/player_status.dart';

/// 队伍条组件
/// 显示当前玩家和队友信息
class TeamBar extends StatelessWidget {
  final PlayerStatus playerStatus;
  final List<PlayerStatus> teammates;

  const TeamBar({
    super.key,
    required this.playerStatus,
    required this.teammates,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          top: BorderSide(
            color: Colors.grey.withOpacity(0.3),
            width: 1,
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题
          Text(
            '队伍信息',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 8),
          
          // 队伍成员列表
          SizedBox(
            height: 80,
            child: ListView(
              scrollDirection: Axis.horizontal,
              children: [
                // 当前玩家
                _buildPlayerCard(context, playerStatus, isCurrentPlayer: true),
                
                // 队友
                ...teammates.map((teammate) => 
                  _buildPlayerCard(context, teammate, isCurrentPlayer: false)
                ),
                
                // 空位占位符（最多4人队伍）
                ...List.generate(
                  (4 - 1 - teammates.length).clamp(0, 3),
                  (index) => _buildEmptySlot(context),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建玩家信息卡
  Widget _buildPlayerCard(BuildContext context, PlayerStatus player, {required bool isCurrentPlayer}) {
    return Container(
      width: 200,
      margin: const EdgeInsets.only(right: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isCurrentPlayer 
            ? Theme.of(context).colorScheme.primaryContainer
            : Theme.of(context).colorScheme.surfaceVariant,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isCurrentPlayer 
              ? Theme.of(context).colorScheme.primary
              : Colors.grey.withOpacity(0.3),
          width: isCurrentPlayer ? 2 : 1,
        ),
      ),
      child: Row(
        children: [
          // 头像区域
          Stack(
            children: [
              // 头像
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: _getClassColor(player.character.characterClass.name).withOpacity(0.2),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: _getClassColor(player.character.characterClass.name),
                    width: 2,
                  ),
                ),
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        player.character.race.icon,
                        style: const TextStyle(fontSize: 16),
                      ),
                      Text(
                        player.character.characterClass.icon,
                        style: const TextStyle(fontSize: 12),
                      ),
                    ],
                  ),
                ),
              ),
              
              // 性别标识（左下角）
              Positioned(
                left: 0,
                bottom: 0,
                child: Container(
                  width: 16,
                  height: 16,
                  decoration: BoxDecoration(
                    color: player.character.gender == '男' ? Colors.blue : Colors.pink,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.white, width: 1),
                  ),
                  child: Center(
                    child: Text(
                      player.character.gender == '男' ? '♂' : '♀',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ),
              
              // 姓名（头像上方）
              Positioned(
                left: 0,
                right: 0,
                top: -20,
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.7),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    player.character.name,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),
            ],
          ),
          
          const SizedBox(width: 12),
          
          // 状态区域
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 血条
                Row(
                  children: [
                    const Icon(Icons.favorite, color: Colors.red, size: 12),
                    const SizedBox(width: 4),
                    Expanded(
                      child: LinearProgressIndicator(
                        value: player.hpPercentage,
                        backgroundColor: Colors.red.withOpacity(0.2),
                        valueColor: AlwaysStoppedAnimation<Color>(
                          player.isNearDeath ? Colors.orange : Colors.red,
                        ),
                        minHeight: 6,
                      ),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '${player.currentHp}/${player.maxHp}',
                      style: const TextStyle(fontSize: 10),
                    ),
                  ],
                ),
                
                const SizedBox(height: 4),
                
                // 魔法条
                Row(
                  children: [
                    const Icon(Icons.auto_awesome, color: Colors.blue, size: 12),
                    const SizedBox(width: 4),
                    Expanded(
                      child: LinearProgressIndicator(
                        value: player.mpPercentage,
                        backgroundColor: Colors.blue.withOpacity(0.2),
                        valueColor: AlwaysStoppedAnimation<Color>(
                          player.isLowMp ? Colors.orange : Colors.blue,
                        ),
                        minHeight: 6,
                      ),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '${player.currentMp}/${player.maxMp}',
                      style: const TextStyle(fontSize: 10),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建空位占位符
  Widget _buildEmptySlot(BuildContext context) {
    return Container(
      width: 80,
      margin: const EdgeInsets.only(right: 12),
      decoration: BoxDecoration(
        color: Colors.grey.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.grey.withOpacity(0.3),
          style: BorderStyle.solid,
        ),
      ),
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.person_add,
              color: Colors.grey,
              size: 24,
            ),
            SizedBox(height: 4),
            Text(
              '空位',
              style: TextStyle(
                color: Colors.grey,
                fontSize: 10,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 获取职业颜色
  Color _getClassColor(String className) {
    switch (className) {
      case 'Fighter':
        return Colors.brown;
      case 'Rogue':
        return Colors.grey;
      case 'Mage':
        return Colors.blue;
      case 'Cleric':
        return Colors.amber;
      default:
        return Colors.grey;
    }
  }
}
