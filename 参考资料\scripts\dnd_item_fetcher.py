#!/usr/bin/env python3
"""
D&D 5e 物品数据获取脚本
从Open5e API获取物品原始数据并保存为JSON格式
"""

import requests
import json
import time
from pathlib import Path
import re

class ItemDataFetcher:
    def __init__(self):
        self.open5e_base = "https://api.open5e.com"
    
    def sanitize_filename(self, name):
        """清理文件名，移除非法字符"""
        # 移除或替换文件名中的非法字符（注意：这里应该是保留合法字符，而不是移除）
        name = re.sub(r'[<>:"/\\|?*]', '_', name)
        name = name.strip()
        return name
    
    def fetch_and_save_items(self, output_dir="data/item", page_size=100):
        """从Open5e API获取所有类型的物品数据并批量保存"""
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 定义要获取的物品类型端点
        item_endpoints = [
            ('magicitems', '魔法物品', 'v1'),
            ('weapons', '武器', 'v2'),
            ('armor', '护甲', 'v2')
        ]
        
        total_fetched = 0
        total_saved = 0
        total_skipped = 0
        
        print("正在从Open5e API获取物品数据...")
        print("注意：Open5e API主要包含以下物品类型：")
        print("- 魔法物品 (Magic Items)")
        print("- 武器 (Weapons)")  
        print("- 护甲 (Armor)")
        print("- 其他装备类型（如冒险装备、工具等）可能包含在sections端点中，但不是结构化的物品数据")
        
        for endpoint, item_type, version in item_endpoints:
            print(f"\n正在获取{item_type}数据...")
            
            # 为每种物品类型创建子目录
            type_output_path = output_path / endpoint
            type_output_path.mkdir(parents=True, exist_ok=True)
            
            page = 1
            
            while True:
                url = f"{self.open5e_base}/{version}/{endpoint}/"
                    
                params = {
                    'page_size': page_size,
                    'page': page
                }
                
                try:
                    response = requests.get(url, params=params)
                    response.raise_for_status()
                    data = response.json()
                    
                    items = data['results']
                    total_fetched += len(items)
                    
                    print(f"  第 {page} 页：获取到 {len(items)} 个{item_type}")
                    
                    # 保存这一批物品
                    batch_saved, batch_skipped = self.save_items_batch(items, type_output_path, item_type)
                    total_saved += batch_saved
                    total_skipped += batch_skipped
                    
                    print(f"    - 已保存：{batch_saved} 个")
                    print(f"    - 已跳过：{batch_skipped} 个")
                    
                    if not data.get('next'):
                        break
                    
                    page += 1
                    time.sleep(0.5)  # 避免请求过快
                    
                except requests.RequestException as e:
                    print(f"  请求失败：{e}")
                    break
        
        print(f"\n✅ 数据获取完成！")
        print(f"总共获取：{total_fetched} 个物品")
        print(f"新保存文件：{total_saved} 个物品")
        print(f"跳过已存在：{total_skipped} 个物品")
        print(f"数据保存在：{output_path}")
        
        print(f"\n📝 说明：")
        print(f"• 本程序获取了Open5e API中结构化的物品数据")
        print(f"• 其他装备类型（如冒险装备、工具等）在D&D中确实存在，但Open5e API没有提供专门的端点")
        print(f"• 如需获取更全面的装备数据，可考虑使用DnD5eAPI (www.dnd5eapi.co) 或其他数据源")
        
        return total_fetched, total_saved, total_skipped
    
    def save_items_batch(self, items, output_path, item_type="物品"):
        """保存一批物品数据"""
        saved_count = 0
        skipped_count = 0
        
        for item in items:
            name = item.get('name', 'Unknown')
            safe_name = self.sanitize_filename(name)
            filename = f"{safe_name}.json"
            filepath = output_path / filename
            
            # 添加调试输出
            print(f"    处理物品: {name}")
            print(f"    安全文件名: {safe_name}")
            print(f"    文件路径: {filepath}")
            
            # 检查文件是否已存在
            if filepath.exists():
                print(f"    文件已存在，跳过")
                skipped_count += 1
                continue
            
            try:
                with open(filepath, 'w', encoding='utf-8') as f:
                    json.dump(item, f, ensure_ascii=False, indent=2)
                print(f"    保存成功")
                saved_count += 1
            except Exception as e:
                print(f"    保存失败 {name}：{e}")
        
        return saved_count, skipped_count

def main():
    fetcher = ItemDataFetcher()
    
    print("从Open5e API获取D&D物品数据")
    print("包括：魔法物品、武器、护甲")
    print("各个物品保存为单独的JSON文件，按类型分类存储")
    print("已存在的文件将被跳过")
    
    confirm = input("\n是否开始获取数据？(y/n): ").strip().lower()
    
    if confirm == 'y':
        fetcher.fetch_and_save_items()
    else:
        print("已取消")

if __name__ == "__main__":
    main()