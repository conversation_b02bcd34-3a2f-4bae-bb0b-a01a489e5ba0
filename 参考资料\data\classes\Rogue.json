{"index": "rogue", "name": "Rogue", "hit_die": 8, "proficiency_choices": [{"desc": "Choose four from Acrobatics, Athletics, Deception, Insight, Intimidation, Investigation, Perception, Performance, Persuasion, Sleight of Hand, and Stealth", "choose": 4, "type": "proficiencies", "from": {"option_set_type": "options_array", "options": [{"option_type": "reference", "item": {"index": "skill-acrobatics", "name": "Skill: Acrobatics", "url": "/api/2014/proficiencies/skill-acrobatics"}}, {"option_type": "reference", "item": {"index": "skill-athletics", "name": "Skill: Athletics", "url": "/api/2014/proficiencies/skill-athletics"}}, {"option_type": "reference", "item": {"index": "skill-deception", "name": "Skill: Deception", "url": "/api/2014/proficiencies/skill-deception"}}, {"option_type": "reference", "item": {"index": "skill-insight", "name": "Skill: Insight", "url": "/api/2014/proficiencies/skill-insight"}}, {"option_type": "reference", "item": {"index": "skill-intimidation", "name": "Skill: Intimidation", "url": "/api/2014/proficiencies/skill-intimidation"}}, {"option_type": "reference", "item": {"index": "skill-investigation", "name": "Skill: Investigation", "url": "/api/2014/proficiencies/skill-investigation"}}, {"option_type": "reference", "item": {"index": "skill-perception", "name": "Skill: Perception", "url": "/api/2014/proficiencies/skill-perception"}}, {"option_type": "reference", "item": {"index": "skill-performance", "name": "Skill: Performance", "url": "/api/2014/proficiencies/skill-performance"}}, {"option_type": "reference", "item": {"index": "skill-persuasion", "name": "Skill: Per<PERSON><PERSON><PERSON>", "url": "/api/2014/proficiencies/skill-persuasion"}}, {"option_type": "reference", "item": {"index": "skill-sleight-of-hand", "name": "Skill: Sleight of Hand", "url": "/api/2014/proficiencies/skill-sleight-of-hand"}}, {"option_type": "reference", "item": {"index": "skill-stealth", "name": "Skill: <PERSON>eal<PERSON>", "url": "/api/2014/proficiencies/skill-stealth"}}]}}], "proficiencies": [{"index": "light-armor", "name": "Light Armor", "url": "/api/2014/proficiencies/light-armor"}, {"index": "simple-weapons", "name": "Simple Weapons", "url": "/api/2014/proficiencies/simple-weapons"}, {"index": "longswords", "name": "Longswords", "url": "/api/2014/proficiencies/longswords"}, {"index": "rapiers", "name": "Rapiers", "url": "/api/2014/proficiencies/rapiers"}, {"index": "shortswords", "name": "Shortswords", "url": "/api/2014/proficiencies/shortswords"}, {"index": "hand-crossbows", "name": "Hand crossbows", "url": "/api/2014/proficiencies/hand-crossbows"}, {"index": "thieves-tools", "name": "Thieves' Tools", "url": "/api/2014/proficiencies/thieves-tools"}, {"index": "saving-throw-dex", "name": "Saving Throw: DEX", "url": "/api/2014/proficiencies/saving-throw-dex"}, {"index": "saving-throw-int", "name": "Saving Throw: INT", "url": "/api/2014/proficiencies/saving-throw-int"}], "saving_throws": [{"index": "dex", "name": "DEX", "url": "/api/2014/ability-scores/dex"}, {"index": "int", "name": "INT", "url": "/api/2014/ability-scores/int"}], "starting_equipment": [{"equipment": {"index": "leather-armor", "name": "<PERSON><PERSON> Armor", "url": "/api/2014/equipment/leather-armor"}, "quantity": 1}, {"equipment": {"index": "dagger", "name": "<PERSON>gger", "url": "/api/2014/equipment/dagger"}, "quantity": 2}, {"equipment": {"index": "thieves-tools", "name": "Thieves' Tools", "url": "/api/2014/equipment/thieves-tools"}, "quantity": 1}], "starting_equipment_options": [{"desc": "(a) a rapier or (b) a shortsword", "choose": 1, "type": "equipment", "from": {"option_set_type": "options_array", "options": [{"option_type": "counted_reference", "count": 1, "of": {"index": "rapier", "name": "<PERSON><PERSON>", "url": "/api/2014/equipment/rapier"}}, {"option_type": "counted_reference", "count": 1, "of": {"index": "shortsword", "name": "Shortsword", "url": "/api/2014/equipment/shortsword"}}]}}, {"desc": "(a) a shortbow and quiver of 20 arrows or (b) a shortsword", "choose": 1, "type": "equipment", "from": {"option_set_type": "options_array", "options": [{"option_type": "multiple", "items": [{"option_type": "counted_reference", "count": 1, "of": {"index": "shortbow", "name": "Shortbow", "url": "/api/2014/equipment/shortbow"}}, {"option_type": "counted_reference", "count": 20, "of": {"index": "arrow", "name": "Arrow", "url": "/api/2014/equipment/arrow"}}]}, {"option_type": "counted_reference", "count": 1, "of": {"index": "shortsword", "name": "Shortsword", "url": "/api/2014/equipment/shortsword"}}]}}, {"desc": "(a) a burglar’s pack, (b) a dungeoneer’s pack, or (c) an explorer’s pack", "choose": 1, "type": "equipment", "from": {"option_set_type": "options_array", "options": [{"option_type": "counted_reference", "count": 1, "of": {"index": "burglars-pack", "name": "<PERSON><PERSON><PERSON>'s Pack", "url": "/api/2014/equipment/burglars-pack"}}, {"option_type": "counted_reference", "count": 1, "of": {"index": "dungeoneers-pack", "name": "Dungeoneer's Pack", "url": "/api/2014/equipment/dungeoneers-pack"}}, {"option_type": "counted_reference", "count": 1, "of": {"index": "explorers-pack", "name": "Explorer's Pack", "url": "/api/2014/equipment/explorers-pack"}}]}}], "class_levels": "/api/2014/classes/rogue/levels", "multi_classing": {"prerequisites": [{"ability_score": {"index": "dex", "name": "DEX", "url": "/api/2014/ability-scores/dex"}, "minimum_score": 13}], "proficiencies": [{"index": "light-armor", "name": "Light Armor", "url": "/api/2014/proficiencies/light-armor"}, {"index": "thieves-tools", "name": "Thieves' Tools", "url": "/api/2014/proficiencies/thieves-tools"}], "proficiency_choices": [{"choose": 1, "type": "proficiencies", "from": {"option_set_type": "options_array", "options": [{"option_type": "reference", "item": {"index": "skill-acrobatics", "name": "Skill: Acrobatics", "url": "/api/2014/proficiencies/skill-acrobatics"}}, {"option_type": "reference", "item": {"index": "skill-athletics", "name": "Skill: Athletics", "url": "/api/2014/proficiencies/skill-athletics"}}, {"option_type": "reference", "item": {"index": "skill-deception", "name": "Skill: Deception", "url": "/api/2014/proficiencies/skill-deception"}}, {"option_type": "reference", "item": {"index": "skill-insight", "name": "Skill: Insight", "url": "/api/2014/proficiencies/skill-insight"}}, {"option_type": "reference", "item": {"index": "skill-intimidation", "name": "Skill: Intimidation", "url": "/api/2014/proficiencies/skill-intimidation"}}, {"option_type": "reference", "item": {"index": "skill-investigation", "name": "Skill: Investigation", "url": "/api/2014/proficiencies/skill-investigation"}}, {"option_type": "reference", "item": {"index": "skill-perception", "name": "Skill: Perception", "url": "/api/2014/proficiencies/skill-perception"}}, {"option_type": "reference", "item": {"index": "skill-performance", "name": "Skill: Performance", "url": "/api/2014/proficiencies/skill-performance"}}, {"option_type": "reference", "item": {"index": "skill-persuasion", "name": "Skill: Per<PERSON><PERSON><PERSON>", "url": "/api/2014/proficiencies/skill-persuasion"}}, {"option_type": "reference", "item": {"index": "skill-sleight-of-hand", "name": "Skill: Sleight of Hand", "url": "/api/2014/proficiencies/skill-sleight-of-hand"}}, {"option_type": "reference", "item": {"index": "skill-stealth", "name": "Skill: <PERSON>eal<PERSON>", "url": "/api/2014/proficiencies/skill-stealth"}}]}}]}, "subclasses": [{"index": "thief", "name": "<PERSON>hief", "url": "/api/2014/subclasses/thief"}], "url": "/api/2014/classes/rogue", "updated_at": "2025-04-08T21:13:56.155Z"}