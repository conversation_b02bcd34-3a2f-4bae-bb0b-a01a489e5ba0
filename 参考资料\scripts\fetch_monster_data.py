#!/usr/bin/env python3
"""
D&D 5e 怪物数据获取脚本
从Open5e API获取怪物原始数据并保存为JSON格式
"""

import requests
import json
import time
from pathlib import Path
import re

class MonsterDataFetcher:
    def __init__(self):
        self.open5e_base = "https://api.open5e.com"
        
    def sanitize_filename(self, name):
        """清理文件名，移除非法字符"""
        # 移除或替换文件名中的非法字符
        name = re.sub(r'[<>:"/\\|?*]', '_', name)
        name = name.strip()
        return name
        
    def fetch_and_save_monsters(self, output_dir="data/monsters", page_size=100):
        """从Open5e API获取怪物数据并逐批保存"""
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        page = 1
        total_fetched = 0
        total_saved = 0
        total_skipped = 0
        
        print("正在从Open5e API获取怪物数据...")
        
        while True:
            url = f"{self.open5e_base}/v1/monsters/"
            params = {
                'page_size': page_size,
                'page': page
            }
            
            try:
                response = requests.get(url, params=params)
                response.raise_for_status()
                data = response.json()
                
                monsters = data['results']
                total_fetched += len(monsters)
                
                print(f"第 {page} 页：获取到 {len(monsters)} 个怪物")
                
                # 保存这一批怪物
                batch_saved, batch_skipped = self.save_monsters_batch(monsters, output_path)
                total_saved += batch_saved
                total_skipped += batch_skipped
                
                print(f"  - 已保存: {batch_saved} 个")
                print(f"  - 已跳过: {batch_skipped} 个")
                
                if not data.get('next'):
                    break
                    
                page += 1
                time.sleep(0.5)  # 避免请求过快
                
            except requests.RequestException as e:
                print(f"请求失败: {e}")
                break
        
        print(f"\n✅ 数据获取完成！")
        print(f"总共获取: {total_fetched} 个怪物")
        print(f"新保存: {total_saved} 个怪物")
        print(f"跳过已存在: {total_skipped} 个怪物")
        print(f"数据保存在: {output_path}")
                
        return total_fetched, total_saved, total_skipped
    
    def save_monsters_batch(self, monsters, output_path):
        """保存一批怪物数据"""
        saved_count = 0
        skipped_count = 0
        
        for monster in monsters:
            name = monster.get('name', 'Unknown')
            safe_name = self.sanitize_filename(name)
            filename = f"{safe_name}.json"
            filepath = output_path / filename
            
            # 检查文件是否已存在
            if filepath.exists():
                skipped_count += 1
                continue
            
            try:
                with open(filepath, 'w', encoding='utf-8') as f:
                    json.dump(monster, f, ensure_ascii=False, indent=2)
                saved_count += 1
            except Exception as e:
                print(f"保存失败 {name}: {e}")
        
        return saved_count, skipped_count

def main():
    fetcher = MonsterDataFetcher()
    
    print("从Open5e API获取怪物原始数据")
    print("每个怪物将保存为单独的JSON文件")
    print("已存在的文件将被跳过")
    
    confirm = input("\n是否开始获取数据？(y/n): ").strip().lower()
    
    if confirm == 'y':
        fetcher.fetch_and_save_monsters()
    else:
        print("已取消")

if __name__ == "__main__":
    main() 