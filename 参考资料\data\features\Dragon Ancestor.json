{"index": "dragon-ancestor", "class": {"index": "sorcerer", "name": "Sorcerer", "url": "/api/2014/classes/sorcerer"}, "subclass": {"index": "draconic", "name": "Draconic", "url": "/api/2014/subclasses/draconic"}, "name": "Dragon Ancestor", "level": 1, "prerequisites": [], "desc": ["At 1st level, you choose one type of dragon as your ancestor. The damage type associated with each dragon is used by features you gain later.", "You can speak, read, and write Draconic. Additionally, whenever you make a Charisma check when interacting with dragons, your proficiency bonus is doubled if it applies to the check."], "feature_specific": {"subfeature_options": {"choose": 1, "type": "feature", "from": {"option_set_type": "options_array", "options": [{"option_type": "reference", "item": {"index": "dragon-ancestor-black---acid-damage", "name": "Dragon Ancestor: Black - Acid Damage", "url": "/api/2014/features/dragon-ancestor-black---acid-damage"}}, {"option_type": "reference", "item": {"index": "dragon-ancestor-blue---lightning-damage", "name": "Dragon Ancestor: Blue - Lightning Damage", "url": "/api/2014/features/dragon-ancestor-blue---lightning-damage"}}, {"option_type": "reference", "item": {"index": "dragon-ancestor-brass---fire-damage", "name": "Dragon Ancestor: Brass - Fire Damage", "url": "/api/2014/features/dragon-ancestor-brass---fire-damage"}}, {"option_type": "reference", "item": {"index": "dragon-ancestor-bronze---lightning-damage", "name": "Dragon Ancestor: Bronze - Lightning Damage", "url": "/api/2014/features/dragon-ancestor-bronze---lightning-damage"}}, {"option_type": "reference", "item": {"index": "dragon-ancestor-copper---acid-damage", "name": "Dragon Ancestor: Copper - Acid Damage", "url": "/api/2014/features/dragon-ancestor-copper---acid-damage"}}, {"option_type": "reference", "item": {"index": "dragon-ancestor-gold---fire-damage", "name": "Dragon Ancestor: Gold - Fire Damage", "url": "/api/2014/features/dragon-ancestor-gold---fire-damage"}}, {"option_type": "reference", "item": {"index": "dragon-ancestor-green---poison-damage", "name": "Dragon Ancestor: Green - Poison Damage", "url": "/api/2014/features/dragon-ancestor-green---poison-damage"}}, {"option_type": "reference", "item": {"index": "dragon-ancestor-red---fire-damage", "name": "Dragon Ancestor: Red - Fire Damage", "url": "/api/2014/features/dragon-ancestor-red---fire-damage"}}, {"option_type": "reference", "item": {"index": "dragon-ancestor-silver---cold-damage", "name": "Dragon Ancestor: Silver - Cold Damage", "url": "/api/2014/features/dragon-ancestor-silver---cold-damage"}}, {"option_type": "reference", "item": {"index": "dragon-ancestor-white---cold-damage", "name": "Dragon Ancestor: White - Cold Damage", "url": "/api/2014/features/dragon-ancestor-white---cold-damage"}}]}}, "invocations": []}, "url": "/api/2014/features/dragon-ancestor", "updated_at": "2025-04-08T21:14:01.855Z"}