# 角色创建系统设计文档

本文档详细说明基于Microlite20规则的角色创建系统，包括UI设计、交互流程和系统实现。

## 1. 系统概述

### 1.1 功能目标
- 提供直观的角色创建界面
- 支持Microlite20规则的完整创角流程
- 适配移动设备和Web平台
- 确保数据准确性和用户体验

### 1.2 创角流程
1. 基本信息输入（姓名、性别、年龄）
2. 种族选择（人类、精灵、矮人、半身人）
3. 职业选择（战士、盗贼、法师、牧师）
4. 属性与生命值掷骰
5. 角色信息确认

## 2. UI界面设计

### 2.1 整体布局
```
┌─────────────────────────────────────────────────────────┐
│                      顶部标题栏                          │
├─────────────────────────────────────────────────────────┤
│                                                         │
│                    内容区域                              │
│                  (可向下滚动)                            │
│                                                         │
├─────────────────────────────────────────────────────────┤
│                     底部操作栏                          │
└─────────────────────────────────────────────────────────┘
```

### 2.2 主界面内容区域
```
┌─────────────────────────────────────────────────────────┐
│  1. 基本信息                                             │
│  ════════════                                           │
│  角色姓名: [____________]                               │
│  性别: [  男  ] [  女  ] [ 其他 ]                      │
│  年龄: [____]                                          │
│                                                         │
│─────────────────────────────────────────────────────────│
│  2. 种族选择                                             │
│  ════════════                                           │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐       │
│  │  人类   │ │  精灵   │ │  矮人   │ │ 半身人  │       │
│  └─────────┘ └─────────┘ └─────────┘ └─────────┘       │
│  选中种族优势: MIND +2，天生的施法者，适合法师、牧师     │
│                                                         │
│─────────────────────────────────────────────────────────│
│  3. 职业选择                                             │
│  ════════════                                           │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐       │
│  │  战士   │ │  盗贼   │ │  法师   │ │  牧师   │       │
│  └─────────┘ └─────────┘ └─────────┘ └─────────┘       │
│  选中职业特点: 前排肉盾，可穿任何护甲，Physical+3       │
│                                                         │
│─────────────────────────────────────────────────────────│
│  4. 当前货币                                             │
│  ════════════                                           │
│  起始金币: 150gp (战士)                                 │
│                                                         │
│─────────────────────────────────────────────────────────│
│  5. 属性与生命值掷骰                                     │
│  ══════════════════════                                 │
│  生命值: 20 (14+1d6)                                   │
│                                                         │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐                   │
│  │  STR    │ │  DEX    │ │  MIND   │                   │
│  │  力量    │ │  敏捷    │ │  心智    │                   │
│  │   14    │ │   12    │ │   15    │                   │
│  └─────────┘ └─────────┘ └─────────┘                   │
│                                                         │
│  [重新掷骰所有属性和生命值]                              │
│                                                         │
└─────────────────────────────────────────────────────────┘
```

### 2.3 确认弹窗设计
```
┌─────────────────────────────────────────────────────────┐
│                      角色创建确认                        │
├─────────────────────────────────────────────────────────┤
│  ┌─────────────────────────────────────────────────┐   │
│  │ 角色信息总览                                    │   │
│  │ ─────────────                                   │   │
│  │ 姓名: 艾莉娜                                    │   │
│  │ 性别: 女                                        │   │
│  │ 年龄: 25                                        │   │
│  │                                                 │   │
│  │ 种族: 精灵                                      │   │
│  │ 职业: 战士                                      │   │
│  │                                                 │   │
│  │ 属性:                                           │   │
│  │ STR: 14  DEX: 12  MIND: 17 (15+2)              │   │
│  │                                                 │   │
│  │ 生命值: 20                                      │   │
│  │ 护甲等级: 11 (10+1)                            │   │
│  │ 起始金币: 150gp                                │   │
│  │                                                 │   │
│  │ 技能专精:                                       │   │
│  │ • Physical +3                                   │   │
│  │ • 所有技能掷骰 +1 (种族)                       │   │
│  └─────────────────────────────────────────────────┘   │
│                                                         │
│    [返回修改]                        [确认创建]         │
└─────────────────────────────────────────────────────────┘
```

## 3. 游戏规则实现

### 3.1 属性系统
角色有3个核心属性：
- **STR (力量)**: 影响近战攻击、伤害和体力相关检定
- **DEX (敏捷)**: 影响远程攻击、护甲等级和灵活性检定  
- **MIND (心智)**: 影响魔法攻击、法术DC和知识相关检定

**属性生成方法**：
- 掷3d6，总和直接作为一个属性值
- 重复3次，分别生成STR、DEX、MIND
- 支持无限次重新掷骰
- 属性加成 = (属性值-10)/2，向下取整

### 3.2 种族特性

#### 人类
- **特性**: 所有技能掷骰+1
- **优势**: 全能型，适合任何职业

#### 精灵
- **特性**: MIND +2
- **优势**: 高智力，天生的施法者
- **推荐职业**: 法师、牧师

#### 矮人
- **特性**: STR +2
- **优势**: 强壮有力，擅长近战
- **推荐职业**: 战士

#### 半身人
- **特性**: DEX +2
- **优势**: 敏捷灵活，擅长潜行
- **推荐职业**: 盗贼

### 3.3 职业特性

#### 战士 (Fighter)
- **护甲**: 可穿任何护甲和使用盾牌
- **专精技能**: Physical +3
- **起始金币**: 150gp
- **特点**: 前排肉盾，高生存能力

#### 盗贼 (Rogue)  
- **护甲**: 只能穿轻甲
- **专精技能**: Subterfuge +3
- **起始金币**: 125gp
- **特点**: 高机动性，擅长偷袭

#### 法师 (Mage)
- **护甲**: 不能穿护甲
- **专精技能**: Knowledge +3
- **起始金币**: 75gp
- **特点**: 强大法术输出，但防御脆弱

#### 牧师 (Cleric)
- **护甲**: 可穿轻甲和中甲
- **专精技能**: Communication +3
- **起始金币**: 120gp
- **特点**: 治疗支援，多用途

## 4. 交互设计

### 4.1 基本信息交互
- 姓名、年龄输入框支持实时输入
- 性别卡片式按钮点击切换
- 必填项验证，未填写时显示提示

### 4.2 种族职业选择交互
- 点击卡片切换选择状态
- 选中状态有明显视觉反馈
- 选择后立即显示详细信息
- 种族选择影响最终属性计算
- 职业选择影响起始金币显示

### 4.3 掷骰交互
- 统一掷骰按钮同时重掷所有属性和生命值
- 显示掷骰动画效果
- 无重掷次数限制
- 掷骰结果立即更新显示

### 4.4 表单验证
- **必填项**: 角色姓名、年龄
- **选择项**: 确保种族和职业已选择
- **按钮状态**: 验证通过前"确认创建"按钮保持禁用

## 5. 移动设备适配

### 5.1 布局适配
- 种族/职业选择：屏幕宽度不足时自动换行为2x2排列
- 属性卡片：小屏幕上垂直排列或调整为较小尺寸
- 性别选择：改为按钮式卡片，水平排列

### 5.2 触摸优化
- 最小触摸目标：44px×44px
- 按钮间距：至少8px避免误触
- 输入框高度：48px以上
- 整个卡片区域可点击

### 5.3 交互优化
- 点击时有明显的视觉反馈
- 选中状态使用高亮边框、背景色变化
- 平滑滚动，适配手势操作
- 键盘适配，避免被遮挡

## 6. 数据计算

### 6.1 自动计算逻辑
- **种族加成**: 自动应用到对应属性
- **起始金币**: 根据职业自动计算
- **生命值**: STR + 1d6掷骰结果
- **护甲等级**: 10 + DEX加成（基础状态）
- **技能专精**: 根据职业确定专精技能

### 6.2 掷骰机制
- **属性掷骰**: 每个属性独立3d6
- **生命值掷骰**: STR属性值 + 1d6
- **统一重掷**: 一个按钮同时重掷所有属性和生命值
- **无限重掷**: 用户可以无限次重新掷骰

## 7. 开发实现要点

### 7.1 状态管理
- 使用响应式数据绑定
- 实时计算和更新派生数据
- 表单验证状态管理

### 7.2 数据验证
- 前端表单验证
- 掷骰结果合法性检查
- 角色数据完整性验证

### 7.3 用户体验
- 触摸友好的界面设计
- 适配不同屏幕尺寸和方向
- 优化触摸反馈和动画效果
- 考虑单手操作的便利性

---

*最后更新：2025-06-14*
